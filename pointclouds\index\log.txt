INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 0, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 2'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 3'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 4'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 5'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 6'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 7'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 8'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 9'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 10'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 11'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 12'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 13'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 14'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 15'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 16'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 17'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 18'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 19'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 21'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 20'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 26'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 24'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 28'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 23'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 27'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 22'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 25'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 32'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 33'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 30'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 37'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 36'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 31'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 34'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 29'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 35'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 39'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 38'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 41'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 40'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 42'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 43'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 44'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 45'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 48'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 49'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 50'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 46'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 47'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 53'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 51'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 54'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 56'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 52'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 55'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 57'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 58'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 59'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 60'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 61'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 63'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 62'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 66'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 64'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 65'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 68'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 67'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 69'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 71'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 70'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 75'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 74'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 73'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 72'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 77'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 76'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 79'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 78'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 80'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 81'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 82'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 83'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 84'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 85'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 86'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 87'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 90'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 89'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 88'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 91'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 93'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 94'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 92'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 95'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 96'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 97'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 98'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 99'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 100'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 101'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 102'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 103'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 104'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 105'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 107'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 108'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 106'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 110'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 109'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 111'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 112'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 113'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 114'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 115'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 116'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 117'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 118'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 119'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 120'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 121'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 122'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 123'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 124'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 125'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 126'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 127'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 130'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 131'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 129'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 128'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 134'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 133'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 132'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 137'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 135'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 136'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 138'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 139'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 140'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 141'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 142'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 143'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 144'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 145'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 146'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 147'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 148'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 149'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 150'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 151'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 152'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 153'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 154'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 155'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 156'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 157'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 158'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 159'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 160'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 162'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 161'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 163'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 165'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 166'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 167'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 164'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 168'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 169'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 170'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 171'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 172'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 175'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 174'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 173'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 176'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 177'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 178'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 180'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 179'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 182'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 183'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 181'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 185'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 187'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 186'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 184'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 188'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 189'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 190'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 191'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 192'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 194'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 195'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 193'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 196'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 198'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 197'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 201'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 199'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 202'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 200'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 204'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 205'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 203'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 207'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 206'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 208'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 209'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 210'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 211'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 212'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 213'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 214'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 215'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 216'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 220'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 219'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 218'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 217'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 221'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 224'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 223'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 226'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 227'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 225'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 222'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 228'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 229'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 230'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 231'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 232'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 233'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 234'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 236'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 235'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 237'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 239'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 238'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 241'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 240'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 243'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 242'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 244'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 248'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 249'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 245'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 246'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 247'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 250'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 251'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 252'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 253'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 256'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 254'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 255'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 257'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 259'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 258'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 265'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 262'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 260'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 264'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 261'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 263'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 270'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 267'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 268'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 269'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 266'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 271'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 272'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 273'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 274'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 275'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 276'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 277'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 279'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 278'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 280'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 281'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 282'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 284'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 283'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 286'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 287'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 285'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 288'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 289'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 290'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 291'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 292'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 293'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 294'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 295'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 297'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 296'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 298'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 299'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 300'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 301'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 302'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 304'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 303'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 305'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 309'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 310'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 307'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 306'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 311'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 308'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 312'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 313'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 314'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 315'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 318'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 317'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 316'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 320'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 319'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 326'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 322'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 324'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 323'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 321'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 325'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 328'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 329'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 330'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 327'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 331'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 332'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 333'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 334'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 335'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 336'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 339'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 337'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 338'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 342'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 340'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 345'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 344'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 343'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 341'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 348'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 347'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 346'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 349'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 350'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 351'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 352'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 353'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 354'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 355'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 356'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 358'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 357'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 359'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 362'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 360'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 361'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 363'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 364'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 365'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 368'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 366'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 367'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 370'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 369'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 371'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 373'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 372'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 374'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 376'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 375'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 377'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 378'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 379'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 380'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 384'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 386'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 385'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 382'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 383'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 381'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 387'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 388'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 390'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 389'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 391'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 392'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 394'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 393'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 395'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 396'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 397'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 399'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 398'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 400'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 402'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 401'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 404'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 403'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 406'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 405'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 407'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 408'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 409'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 410'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 411'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 412'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 413'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 414'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 415'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 416'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 417'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 418'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 419'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 420'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 421'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 422'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 423'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 424'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 425'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 426'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 427'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 428'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 429'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 430'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 431'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 432'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 433'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 434'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 435'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 436'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 437'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 438'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 439'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 441'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 440'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 442'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 443'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 445'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 444'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 446'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 447'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 448'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 451'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 450'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 449'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 454'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 453'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 455'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 452'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 457'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 456'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 458'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 461'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 460'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 459'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 462'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 464'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 463'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 465'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 466'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 467'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 468'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 469'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 470'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 471'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 473'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 472'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 474'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 475'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 476'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 477'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 478'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 479'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 480'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 481'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 483'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 482'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 484'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 485'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 486'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 487'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 488'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 489'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 490'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 491'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 492'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 493'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 494'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 495'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 496'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 497'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 498'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 499'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 500'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 501'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 502'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 503'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 504'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 505'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 508'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 507'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 506'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 509'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 510'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 511'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 513'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 512'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 514'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 515'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 516'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 518'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 517'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 519'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 520'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 521'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 522'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 524'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 523'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 525'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 526'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 527'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 528'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 529'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 530'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 532'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 533'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 531'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 534'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 535'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 536'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 537'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 538'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 539'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 540'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 541'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 542'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 543'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 544'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 545'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 547'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 546'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 549'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 548'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 550'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 551'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 553'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 552'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 555'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 554'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 556'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 557'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 558'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 559'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 560'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 561'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 566'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 565'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 563'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 564'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 562'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 567'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 568'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 569'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 570'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 571'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 573'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 572'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 574'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 575'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 577'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 578'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 576'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 579'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 580'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 581'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 582'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 584'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 585'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 583'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 587'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 586'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 588'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 589'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 590'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 592'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 591'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 593'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 594'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 595'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 597'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 596'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 598'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 599'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 600'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 601'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 602'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 603'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 604'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 605'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 607'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 606'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 608'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 609'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 612'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 610'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 611'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 614'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 613'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 615'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 616'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 617'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 618'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 619'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 620'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 621'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 625'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 623'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 622'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 626'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 624'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 627'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 628'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 629'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 631'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 630'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 632'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 633'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 634'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 635'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 636'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 637'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 638'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 639'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 640'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 641'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 642'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 644'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 643'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 646'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 645'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 647'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 648'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 649'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 650'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 651'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 653'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 652'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 654'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 655'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 656'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 657'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 658'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 659'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 660'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 661'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 663'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 662'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 666'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 665'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 667'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 664'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 668'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 669'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 670'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 671'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 672'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 673'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 674'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 675'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 676'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 677'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 679'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 678'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 681'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 680'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 684'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 683'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 682'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 687'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 686'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 685'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 688'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 689'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 690'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 691'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 692'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 693'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 694'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 698'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 696'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 695'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 697'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 701'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 699'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 700'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 702'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 703'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 704'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 705'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 706'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 707'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 708'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 709'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 710'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 712'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 713'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 711'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 714'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 715'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 716'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 719'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 718'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 717'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 720'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 722'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 721'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 723'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 724'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 725'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 726'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 727'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 729'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 728'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 730'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 731'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 732'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 733'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 734'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 735'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 736'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 738'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 737'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 739'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 740'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 741'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 742'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 743'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 744'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 745'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 746'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 747'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 749'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 748'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 751'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 750'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 752'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 753'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 754'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 755'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 756'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 757'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 759'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 758'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 760'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 762'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 761'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 763'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 764'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 765'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 766'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 767'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 768'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 769'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 770'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 771'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 772'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 773'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 774'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 775'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 776'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 777'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 779'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 778'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 781'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 780'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 783'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 782'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 784'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 785'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 787'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 786'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 788'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 790'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 789'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 791'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 792'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 793'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 794'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 795'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 796'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 797'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 799'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 798'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 801'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 800'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 802'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 803'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 804'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 807'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 808'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 805'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 806'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 809'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 810'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 811'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 812'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 813'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 814'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 815'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 816'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 817'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 818'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 819'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 820'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 821'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 822'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 823'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 824'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 825'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 826'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 827'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 829'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 828'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 830'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 833'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 832'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 831'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 834'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 835'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 836'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 838'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 837'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 839'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 840'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 841'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 843'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 842'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 844'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 846'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 847'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 845'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 848'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 850'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 849'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 851'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 852'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 853'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 854'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 856'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 857'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 855'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 858'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 859'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 860'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 861'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 864'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 862'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 863'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 867'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 865'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 866'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 868'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 869'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 870'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 871'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 872'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 873'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 874'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 875'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 876'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 877'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 878'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 879'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 880'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 881'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 882'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 885'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 883'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 884'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 886'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 887'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 888'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 889'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 891'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 890'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 893'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 892'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 896'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 894'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 895'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 898'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 897'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 899'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 900'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 902'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 901'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 903'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 904'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 905'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 906'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 907'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 908'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 909'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 910'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 915'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 914'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 913'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 912'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 911'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 916'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 917'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 919'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 918'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 920'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 921'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 924'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 923'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 922'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 927'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 925'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 926'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 928'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 929'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 930'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 931'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 932'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 933'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 934'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 935'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 936'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 937'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 938'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 939'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 940'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 943'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 944'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 941'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 942'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 946'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 947'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 945'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 948'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 949'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 950'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 951'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 952'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 954'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 953'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 955'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 958'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 956'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 957'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 959'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 963'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 962'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 961'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 960'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 964'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 965'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 966'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 967'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 968'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 969'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 970'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 971'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 972'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 973'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 974'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 975'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 976'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 977'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 978'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 980'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 979'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 981'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 982'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 983'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 984'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 985'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 986'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 987'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 988'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 989'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 990'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 991'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 992'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 993'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 994'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 995'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 996'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 997'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 999'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 998'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'001'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'002'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'006'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'005'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'000'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'004'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'003'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'007'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'008'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'009'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'010'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'011'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'012'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'013'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'014'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'015'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'016'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'017'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'018'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'019'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'022'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'020'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'021'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'025'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'023'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'027'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'028'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'024'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'026'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'030'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'031'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'029'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'032'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'033'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'034'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'037'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'035'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'036'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'038'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'039'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'040'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'041'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'042'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'045'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'044'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'043'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'049'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'048'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'050'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'047'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'046'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'052'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'053'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'051'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'054'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'055'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'056'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'058'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'057'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'059'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'062'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'061'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'060'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'063'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'064'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'065'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'066'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'067'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'069'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'068'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'070'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'071'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'072'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'075'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'073'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'074'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'076'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'077'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'079'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'078'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'080'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'081'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'083'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'082'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'084'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'085'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'086'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'087'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'088'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'089'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'090'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'091'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'093'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'092'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'095'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'096'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'094'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'097'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'099'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'098'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'100'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'101'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'102'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'103'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'106'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'107'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'104'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'105'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'108'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'109'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'110'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'113'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'114'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'111'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'112'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'116'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'115'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'117'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'118'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'119'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'121'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'120'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'122'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'123'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'126'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'124'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'125'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'127'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'128'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'129'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'130'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'131'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'132'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'133'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'134'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'135'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'138'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'137'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'136'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'139'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'141'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'140'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'142'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'143'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'144'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'145'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'147'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'146'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'148'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'149'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'150'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'151'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'154'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'153'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'152'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'155'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'156'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'158'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'157'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'159'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'161'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'160'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'162'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'163'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'164'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'165'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'166'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'167'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'168'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'169'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'170'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'171'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'172'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'175'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'174'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'173'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'178'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'176'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'177'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'179'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'181'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'180'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'182'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'183'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'184'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'185'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'186'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'188'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'187'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'189'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'190'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'191'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'192'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'194'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'195'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'193'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'197'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'196'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'198'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'201'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'200'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'199'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'202'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'203'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'204'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'205'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'206'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'209'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'207'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'208'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'210'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'212'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'211'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'213'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'214'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'215'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'217'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'218'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'216'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'221'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'219'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'220'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'222'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'223'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'224'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'225'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'226'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'228'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'227'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'229'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'230'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'232'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'233'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'231'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'235'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'234'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'236'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'238'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'237'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'241'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'239'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'240'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'242'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'243'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'244'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'245'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'246'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'247'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'248'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'249'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'250'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'251'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'252'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'253'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'254'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'255'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'256'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'257'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'258'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'262'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'259'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'264'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'261'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'263'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'260'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'265'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'266'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'267'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'268'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'269'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'270'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'271'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'272'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'273'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'275'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'274'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'276'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'277'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'278'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'279'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'285'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'281'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'283'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'284'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'280'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'282'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'286'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'287'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'289'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'288'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'290'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'291'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'292'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'293'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'294'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'297'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'296'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'295'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'299'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'298'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'302'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'301'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'304'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'300'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'305'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'303'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'306'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'307'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'308'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'309'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'310'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'311'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'312'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'313'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'314'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'316'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'315'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'317'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'318'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'319'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'321'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'320'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'325'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'322'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'323'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'324'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'326'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'327'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'328'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'329'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'330'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'331'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'332'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'333'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'334'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'335'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'336'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'337'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'342'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'340'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'343'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'344'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'339'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'341'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'338'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'345'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'346'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'347'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'348'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'349'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'350'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'351'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'352'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'353'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'356'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'355'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'354'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'357'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'361'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'359'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'358'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'360'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'362'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'363'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'366'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'364'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'365'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'367'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'368'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'369'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'370'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'371'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'372'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'373'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'374'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'375'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'376'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'378'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'377'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'379'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'382'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'381'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'380'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'386'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'383'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'384'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'385'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'387'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'388'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'389'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'390'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'391'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'392'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'393'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'394'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'397'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'398'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'395'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'396'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'399'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'400'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'402'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'403'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'401'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'404'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'405'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'406'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'407'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'408'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'409'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'410'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'411'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'412'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'413'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'414'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'415'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'417'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'416'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'418'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'419'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'421'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'422'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'423'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'420'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'424'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'425'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'426'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'427'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'428'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'429'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'430'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'431'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'432'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'433'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'434'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'435'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'437'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'438'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'436'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'439'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'442'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'443'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'440'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'441'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'444'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'447'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'446'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'449'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'450'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'448'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'445'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'451'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'452'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'453'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'454'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'455'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'458'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'456'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'457'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'459'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'460'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'461'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'462'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'463'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'464'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'465'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'467'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'466'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'468'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'469'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'470'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'471'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'472'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'473'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'474'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'475'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'476'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'478'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'477'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'479'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'482'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'481'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'480'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'483'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'484'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'485'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'486'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'487'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'488'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'489'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'490'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'492'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'491'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'493'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'495'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'494'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'496'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'497'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'498'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'499'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'500'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'502'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'501'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'503'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'504'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'506'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'507'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'509'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'505'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'508'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'510'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'511'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'512'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'513'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'514'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'517'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'515'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'516'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'520'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'518'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'519'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'521'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'522'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'525'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'523'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'524'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'526'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'528'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'529'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'527'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'530'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'531'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'532'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'534'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'533'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'535'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'536'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'537'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'539'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'538'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'540'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'541'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'542'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'543'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'544'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'545'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'546'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'547'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'549'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'548'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'550'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'551'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'552'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'553'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'554'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'555'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'557'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'559'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'556'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'561'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'560'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'558'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'563'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'564'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'562'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'565'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'566'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'567'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'568'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'569'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'570'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'572'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'571'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'573'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'574'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'575'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'579'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'577'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'578'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'576'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'580'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'581'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'582'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'583'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'584'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'585'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'587'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'586'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'588'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'589'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'590'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'591'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'592'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'594'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'593'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'595'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'599'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'598'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'597'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'596'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'600'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'602'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'603'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'604'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'601'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'606'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'607'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'605'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'608'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'609'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'611'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'610'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'612'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'613'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'614'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'615'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'616'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'617'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'618'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'619'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'620'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'621'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'622'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'623'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'624'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'625'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'626'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'627'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'629'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'628'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'630'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'631'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'633'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'632'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'634'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'635'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'638'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'636'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'637'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'639'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'640'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'641'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'642'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'643'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'644'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'645'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'646'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'647'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'648'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'649'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'650'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'651'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'652'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'653'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'655'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'654'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'656'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'657'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'658'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'659'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'660'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'661'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'662'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'663'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'664'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'665'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'666'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'667'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'669'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'670'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'668'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'671'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'673'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'672'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'674'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'675'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'676'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'677'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'678'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'679'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'680'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'681'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'683'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'684'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'682'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'685'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'686'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'687'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'688'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'689'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'690'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'691'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'692'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'693'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'694'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'696'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'695'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'697'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'698'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'700'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'702'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'699'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'701'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'703'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'705'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'704'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'706'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'707'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'708'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'711'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'709'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'710'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'712'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'713'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'714'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'715'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'716'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'717'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'719'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'720'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'718'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'721'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'722'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'724'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'723'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'725'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'726'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'727'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'728'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'729'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'730'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'731'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'732'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'733'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'734'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'735'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'736'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'737'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'738'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'739'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'740'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'741'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'742'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'743'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'744'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'745'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'746'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'747'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'748'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'749'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'750'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'751'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'752'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'753'000'000, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting pc_merge.las, first point: 1'754'000'000, num points: 455'463
INFO(indexer.cpp:1656): start indexing chunk r0020
filesize: 56'269'377
min: 712298.67000000004, 1424239.86375, 11.300000000000001
max: 712603.99187500007, 1424545.1856249999, 316.62187500000874
INFO(indexer.cpp:1656): start indexing chunk r0022
filesize: 59'344'110
min: 712298.67000000004, 1424545.1856249999, 11.300000000000001
max: 712603.99187500007, 1424850.5074999998, 316.62187500000874
INFO(indexer.cpp:1656): start indexing chunk r0024
filesize: 152'007'786
min: 712603.99187500007, 1424239.86375, 11.300000000000001
max: 712909.31374999997, 1424545.1856249999, 316.62187500000874
INFO(indexer.cpp:1656): start indexing chunk r0026
filesize: 197'894'124
min: 712603.99187500007, 1424545.1856249999, 11.300000000000001
max: 712909.31374999997, 1424850.5074999998, 316.62187500000874
INFO(indexer.cpp:1656): start indexing chunk r004
filesize: 77'349'627
min: 712909.31374999997, 1423629.22, 11.300000000000001
max: 713519.95750000002, 1424239.86375, 621.94375000001742
INFO(indexer.cpp:1656): start indexing chunk r00600
filesize: 55'627'317
min: 712909.31374999997, 1424239.86375, 11.300000000000001
max: 713061.97468749993, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00602
filesize: 140'444'064
min: 712909.31374999997, 1424392.5246875, 11.300000000000001
max: 713061.97468749993, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00604
filesize: 42'846'408
min: 713061.97468749993, 1424239.86375, 11.300000000000001
max: 713214.635625, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00606
filesize: 144'742'464
min: 713061.97468749993, 1424392.5246875, 11.300000000000001
max: 713214.635625, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00620
filesize: 164'058'129
min: 712909.31374999997, 1424545.1856249999, 11.300000000000001
max: 713061.97468749993, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00622
filesize: 165'709'125
min: 712909.31374999997, 1424697.8465624999, 11.300000000000001
max: 713061.97468749993, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00624
filesize: 190'273'266
min: 713061.97468749993, 1424545.1856249999, 11.300000000000001
max: 713214.635625, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00626
filesize: 172'436'553
min: 713061.97468749993, 1424697.8465624999, 11.300000000000001
max: 713214.635625, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00640
filesize: 40'789'926
min: 713214.635625, 1424239.86375, 11.300000000000001
max: 713367.29656250007, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00642
filesize: 179'359'380
min: 713214.635625, 1424392.5246875, 11.300000000000001
max: 713367.29656250007, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00644
filesize: 111'317'274
min: 713367.29656250007, 1424239.86375, 11.300000000000001
max: 713519.95750000002, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00646
filesize: 164'773'710
min: 713367.29656250007, 1424392.5246875, 11.300000000000001
max: 713519.95750000002, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00660
filesize: 172'648'962
min: 713214.635625, 1424545.1856249999, 11.300000000000001
max: 713367.29656250007, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00662
filesize: 163'753'650
min: 713214.635625, 1424697.8465624999, 11.300000000000001
max: 713367.29656250007, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00664
filesize: 181'665'153
min: 713367.29656250007, 1424545.1856249999, 11.300000000000001
max: 713519.95750000002, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r00666
filesize: 164'508'327
min: 713367.29656250007, 1424697.8465624999, 11.300000000000001
max: 713519.95750000002, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r020
filesize: 169'230'330
min: 712298.67000000004, 1424850.5074999998, 11.300000000000001
max: 712909.31374999997, 1425461.1512499999, 621.94375000001742
INFO(indexer.cpp:1656): start indexing chunk r022
filesize: 246'782'484
min: 712298.67000000004, 1425461.1512499999, 11.300000000000001
max: 712909.31374999997, 1426071.7949999999, 621.94375000001742
INFO(indexer.cpp:1656): start indexing chunk r02400
filesize: 150'462'657
min: 712909.31374999997, 1424850.5074999998, 11.300000000000001
max: 713061.97468749993, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00640
INFO(indexer.cpp:1656): start indexing chunk r02402
filesize: 156'366'531
min: 712909.31374999997, 1425003.1684374998, 11.300000000000001
max: 713061.97468749993, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00604
INFO(indexer.cpp:1656): start indexing chunk r02404
filesize: 145'711'035
min: 713061.97468749993, 1424850.5074999998, 11.300000000000001
max: 713214.635625, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0020
INFO(indexer.cpp:1656): start indexing chunk r02406
filesize: 148'607'757
min: 713061.97468749993, 1425003.1684374998, 11.300000000000001
max: 713214.635625, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0022
INFO(indexer.cpp:1656): start indexing chunk r02420
filesize: 145'313'649
min: 712909.31374999997, 1425155.8293749997, 11.300000000000001
max: 713061.97468749993, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00600
INFO(indexer.cpp:1656): start indexing chunk r02422
filesize: 145'799'973
min: 712909.31374999997, 1425308.4903124999, 11.300000000000001
max: 713061.97468749993, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r004
INFO(indexer.cpp:1656): start indexing chunk r02424
filesize: 173'408'256
min: 713061.97468749993, 1425155.8293749997, 11.300000000000001
max: 713214.635625, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00644
INFO(indexer.cpp:1656): start indexing chunk r02426
filesize: 146'249'820
min: 713061.97468749993, 1425308.4903124999, 11.300000000000001
max: 713214.635625, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00646
INFO(indexer.cpp:1656): start indexing chunk r02440
filesize: 148'342'185
min: 713214.635625, 1424850.5074999998, 11.300000000000001
max: 713367.29656250007, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00606
INFO(indexer.cpp:1656): start indexing chunk r02442
filesize: 138'622'347
min: 713214.635625, 1425003.1684374998, 11.300000000000001
max: 713367.29656250007, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00626
INFO(indexer.cpp:1656): start indexing chunk r02444
filesize: 176'126'184
min: 713367.29656250007, 1424850.5074999998, 11.300000000000001
max: 713519.95750000002, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00602
INFO(indexer.cpp:1656): start indexing chunk r02446
filesize: 152'853'615
min: 713367.29656250007, 1425003.1684374998, 11.300000000000001
max: 713519.95750000002, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00642
INFO(indexer.cpp:1656): start indexing chunk r02460
filesize: 138'729'942
min: 713214.635625, 1425155.8293749997, 11.300000000000001
max: 713367.29656250007, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00666
INFO(indexer.cpp:1699): finished indexing chunk r0024
INFO(indexer.cpp:1656): start indexing chunk r02462
filesize: 130'454'361
min: 713214.635625, 1425308.4903124999, 11.300000000000001
max: 713367.29656250007, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r02464
filesize: 181'439'541
min: 713367.29656250007, 1425155.8293749997, 11.300000000000001
max: 713519.95750000002, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00620
INFO(indexer.cpp:1699): finished indexing chunk r00662
INFO(indexer.cpp:1656): start indexing chunk r02466
filesize: 153'149'103
min: 713367.29656250007, 1425308.4903124999, 11.300000000000001
max: 713519.95750000002, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r02600
filesize: 133'362'585
min: 712909.31374999997, 1425461.1512499999, 11.300000000000001
max: 713061.97468749993, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r020
INFO(indexer.cpp:1656): start indexing chunk r02602
filesize: 157'295'763
min: 712909.31374999997, 1425613.8121874998, 11.300000000000001
max: 713061.97468749993, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02400
INFO(indexer.cpp:1656): start indexing chunk r02604
filesize: 140'334'201
min: 713061.97468749993, 1425461.1512499999, 11.300000000000001
max: 713214.635625, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00664
INFO(indexer.cpp:1656): start indexing chunk r02606
filesize: 135'943'650
min: 713061.97468749993, 1425613.8121874998, 11.300000000000001
max: 713214.635625, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00622
INFO(indexer.cpp:1656): start indexing chunk r02620
filesize: 146'838'042
min: 712909.31374999997, 1425766.473125, 11.300000000000001
max: 713061.97468749993, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02402
INFO(indexer.cpp:1656): start indexing chunk r02622
filesize: 147'366'810
min: 712909.31374999997, 1425919.1340625, 11.300000000000001
max: 713061.97468749993, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02404
INFO(indexer.cpp:1656): start indexing chunk r02624
filesize: 148'253'409
min: 713061.97468749993, 1425766.473125, 11.300000000000001
max: 713214.635625, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02422
INFO(indexer.cpp:1656): start indexing chunk r02626
filesize: 144'192'096
min: 713061.97468749993, 1425919.1340625, 11.300000000000001
max: 713214.635625, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00624
INFO(indexer.cpp:1699): finished indexing chunk r02420
INFO(indexer.cpp:1656): start indexing chunk r02640
filesize: 142'762'311
min: 713214.635625, 1425461.1512499999, 11.300000000000001
max: 713367.29656250007, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r02642
filesize: 145'955'871
min: 713214.635625, 1425613.8121874998, 11.300000000000001
max: 713367.29656250007, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r00660
INFO(indexer.cpp:1656): start indexing chunk r02644
filesize: 151'231'374
min: 713367.29656250007, 1425461.1512499999, 11.300000000000001
max: 713519.95750000002, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0026
INFO(indexer.cpp:1656): start indexing chunk r02646
filesize: 185'038'452
min: 713367.29656250007, 1425613.8121874998, 11.300000000000001
max: 713519.95750000002, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02426
INFO(indexer.cpp:1656): start indexing chunk r02660
filesize: 130'859'172
min: 713214.635625, 1425766.473125, 11.300000000000001
max: 713367.29656250007, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02406
INFO(indexer.cpp:1656): start indexing chunk r02662
filesize: 131'952'402
min: 713214.635625, 1425919.1340625, 11.300000000000001
max: 713367.29656250007, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02424
INFO(indexer.cpp:1656): start indexing chunk r02664
filesize: 171'802'674
min: 713367.29656250007, 1425766.473125, 11.300000000000001
max: 713519.95750000002, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r022
INFO(indexer.cpp:1656): start indexing chunk r02666
filesize: 144'039'330
min: 713367.29656250007, 1425919.1340625, 11.300000000000001
max: 713519.95750000002, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02442
INFO(indexer.cpp:1656): start indexing chunk r04020
filesize: 9'226'224
min: 713519.95750000002, 1423934.5418750001, 11.300000000000001
max: 713672.61843749997, 1424087.2028125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04020
INFO(indexer.cpp:1656): start indexing chunk r04022
filesize: 115'842'879
min: 713519.95750000002, 1424087.2028125001, 11.300000000000001
max: 713672.61843749997, 1424239.86375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02440
INFO(indexer.cpp:1656): start indexing chunk r04024
filesize: 7'938'864
min: 713672.61843749997, 1423934.5418750001, 11.300000000000001
max: 713825.27937500004, 1424087.2028125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04024
INFO(indexer.cpp:1656): start indexing chunk r04026
filesize: 154'047'636
min: 713672.61843749997, 1424087.2028125001, 11.300000000000001
max: 713825.27937500004, 1424239.86375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02466
INFO(indexer.cpp:1656): start indexing chunk r04060
filesize: 74'845'377
min: 713825.27937500004, 1423934.5418750001, 11.300000000000001
max: 713977.94031250011, 1424087.2028125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02460
INFO(indexer.cpp:1656): start indexing chunk r04062
filesize: 232'725'582
min: 713825.27937500004, 1424087.2028125001, 11.300000000000001
max: 713977.94031250011, 1424239.86375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02462
INFO(indexer.cpp:1656): start indexing chunk r04064
filesize: 114'251'283
min: 713977.94031250011, 1423934.5418750001, 11.300000000000001
max: 714130.60125000007, 1424087.2028125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02604
INFO(indexer.cpp:1656): start indexing chunk r04066
filesize: 151'210'260
min: 713977.94031250011, 1424087.2028125001, 11.300000000000001
max: 714130.60125000007, 1424239.86375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02600
INFO(indexer.cpp:1656): start indexing chunk r04200
filesize: 127'964'610
min: 713519.95750000002, 1424239.86375, 11.300000000000001
max: 713672.61843749997, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02446
INFO(indexer.cpp:1656): start indexing chunk r04202
filesize: 158'012'667
min: 713519.95750000002, 1424392.5246875, 11.300000000000001
max: 713672.61843749997, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02606
INFO(indexer.cpp:1656): start indexing chunk r04204
filesize: 155'439'351
min: 713672.61843749997, 1424239.86375, 11.300000000000001
max: 713825.27937500004, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02640
INFO(indexer.cpp:1656): start indexing chunk r04206
filesize: 151'753'527
min: 713672.61843749997, 1424392.5246875, 11.300000000000001
max: 713825.27937500004, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02624
INFO(indexer.cpp:1699): finished indexing chunk r02620
INFO(indexer.cpp:1656): start indexing chunk r04220
filesize: 171'454'320
min: 713519.95750000002, 1424545.1856249999, 11.300000000000001
max: 713672.61843749997, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r04222
filesize: 142'054'452
min: 713519.95750000002, 1424697.8465624999, 11.300000000000001
max: 713672.61843749997, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02622
INFO(indexer.cpp:1699): finished indexing chunk r02644
INFO(indexer.cpp:1656): start indexing chunk r04224
filesize: 139'636'710
min: 713672.61843749997, 1424545.1856249999, 11.300000000000001
max: 713825.27937500004, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02660
INFO(indexer.cpp:1656): start indexing chunk r04226
filesize: 136'663'065
min: 713672.61843749997, 1424697.8465624999, 11.300000000000001
max: 713825.27937500004, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02444
INFO(indexer.cpp:1656): start indexing chunk r04240
filesize: 232'184'988
min: 713825.27937500004, 1424239.86375, 11.300000000000001
max: 713977.94031250011, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02626
INFO(indexer.cpp:1656): start indexing chunk r04242
filesize: 256'947'660
min: 713825.27937500004, 1424392.5246875, 11.300000000000001
max: 713977.94031250011, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r04244
filesize: 148'632'570
min: 713977.94031250011, 1424239.86375, 11.300000000000001
max: 714130.60125000007, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02642
INFO(indexer.cpp:1656): start indexing chunk r04246
filesize: 140'712'660
min: 713977.94031250011, 1424392.5246875, 11.300000000000001
max: 714130.60125000007, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02464
INFO(indexer.cpp:1656): start indexing chunk r04260
filesize: 223'259'733
min: 713825.27937500004, 1424545.1856249999, 11.300000000000001
max: 713977.94031250011, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02662
INFO(indexer.cpp:1656): start indexing chunk r04262
filesize: 205'088'004
min: 713825.27937500004, 1424697.8465624999, 11.300000000000001
max: 713977.94031250011, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04060
INFO(indexer.cpp:1656): start indexing chunk r04264
filesize: 134'189'487
min: 713977.94031250011, 1424545.1856249999, 11.300000000000001
max: 714130.60125000007, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02602
INFO(indexer.cpp:1656): start indexing chunk r04266
filesize: 128'124'342
min: 713977.94031250011, 1424697.8465624999, 11.300000000000001
max: 714130.60125000007, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02646
INFO(indexer.cpp:1656): start indexing chunk r04400
filesize: 20'015'613
min: 714130.60125000007, 1423629.22, 11.300000000000001
max: 714283.26218750002, 1423781.8809374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04022
INFO(indexer.cpp:1656): start indexing chunk r04402
filesize: 26'456'544
min: 714130.60125000007, 1423781.8809374999, 11.300000000000001
max: 714283.26218750002, 1423934.5418750001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04400
INFO(indexer.cpp:1656): start indexing chunk r04404
filesize: 147'824'082
min: 714283.26218750002, 1423629.22, 11.300000000000001
max: 714435.92312500009, 1423781.8809374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04064
INFO(indexer.cpp:1656): start indexing chunk r04406
filesize: 180'782'766
min: 714283.26218750002, 1423781.8809374999, 11.300000000000001
max: 714435.92312500009, 1423934.5418750001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04402
INFO(indexer.cpp:1656): start indexing chunk r04420
filesize: 145'614'132
min: 714130.60125000007, 1423934.5418750001, 11.300000000000001
max: 714283.26218750002, 1424087.2028125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02666
INFO(indexer.cpp:1656): start indexing chunk r04422
filesize: 158'158'629
min: 714130.60125000007, 1424087.2028125001, 11.300000000000001
max: 714283.26218750002, 1424239.86375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04026
INFO(indexer.cpp:1656): start indexing chunk r04424
filesize: 168'085'368
min: 714283.26218750002, 1423934.5418750001, 11.300000000000001
max: 714435.92312500009, 1424087.2028125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r02664
INFO(indexer.cpp:1656): start indexing chunk r04426
filesize: 217'889'217
min: 714283.26218750002, 1424087.2028125001, 11.300000000000001
max: 714435.92312500009, 1424239.86375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04200
INFO(indexer.cpp:1656): start indexing chunk r04440
filesize: 154'550'376
min: 714435.92312500009, 1423629.22, 11.300000000000001
max: 714588.58406250016, 1423781.8809374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04222
INFO(indexer.cpp:1656): start indexing chunk r04442
filesize: 147'435'768
min: 714435.92312500009, 1423781.8809374999, 11.300000000000001
max: 714588.58406250016, 1423934.5418750001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04224
INFO(indexer.cpp:1656): start indexing chunk r0446
filesize: 240'011'694
min: 714435.92312500009, 1423934.5418750001, 11.300000000000001
max: 714741.24500000011, 1424239.86375, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r04226
INFO(indexer.cpp:1656): start indexing chunk r04600
filesize: 167'399'811
min: 714130.60125000007, 1424239.86375, 11.300000000000001
max: 714283.26218750002, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04206
INFO(indexer.cpp:1656): start indexing chunk r04602
filesize: 165'484'215
min: 714130.60125000007, 1424392.5246875, 11.300000000000001
max: 714283.26218750002, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04066
INFO(indexer.cpp:1656): start indexing chunk r04604
filesize: 182'601'999
min: 714283.26218750002, 1424239.86375, 11.300000000000001
max: 714435.92312500009, 1424392.5246875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04202
INFO(indexer.cpp:1656): start indexing chunk r04606
filesize: 194'959'737
min: 714283.26218750002, 1424392.5246875, 11.300000000000001
max: 714435.92312500009, 1424545.1856249999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04204
INFO(indexer.cpp:1656): start indexing chunk r04620
filesize: 166'182'867
min: 714130.60125000007, 1424545.1856249999, 11.300000000000001
max: 714283.26218750002, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04220
INFO(indexer.cpp:1656): start indexing chunk r04622
filesize: 155'664'531
min: 714130.60125000007, 1424697.8465624999, 11.300000000000001
max: 714283.26218750002, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04244
INFO(indexer.cpp:1656): start indexing chunk r04624
filesize: 175'561'317
min: 714283.26218750002, 1424545.1856249999, 11.300000000000001
max: 714435.92312500009, 1424697.8465624999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04264
INFO(indexer.cpp:1656): start indexing chunk r04626
filesize: 174'420'270
min: 714283.26218750002, 1424697.8465624999, 11.300000000000001
max: 714435.92312500009, 1424850.5074999998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04246
INFO(indexer.cpp:1656): start indexing chunk r0464
filesize: 254'602'359
min: 714435.92312500009, 1424239.86375, 11.300000000000001
max: 714741.24500000011, 1424545.1856249999, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r04266
INFO(indexer.cpp:1656): start indexing chunk r0466
filesize: 267'588'090
min: 714435.92312500009, 1424545.1856249999, 11.300000000000001
max: 714741.24500000011, 1424850.5074999998, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r04404
INFO(indexer.cpp:1656): start indexing chunk r06000
filesize: 160'155'441
min: 713519.95750000002, 1424850.5074999998, 11.300000000000001
max: 713672.61843749997, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04420
INFO(indexer.cpp:1656): start indexing chunk r06002
filesize: 154'616'121
min: 713519.95750000002, 1425003.1684374998, 11.300000000000001
max: 713672.61843749997, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04062
INFO(indexer.cpp:1656): start indexing chunk r06004
filesize: 138'082'779
min: 713672.61843749997, 1424850.5074999998, 11.300000000000001
max: 713825.27937500004, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04262
INFO(indexer.cpp:1656): start indexing chunk r06006
filesize: 154'887'579
min: 713672.61843749997, 1425003.1684374998, 11.300000000000001
max: 713825.27937500004, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04406
INFO(indexer.cpp:1656): start indexing chunk r06020
filesize: 140'063'310
min: 713519.95750000002, 1425155.8293749997, 11.300000000000001
max: 713672.61843749997, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04422
INFO(indexer.cpp:1656): start indexing chunk r06022
filesize: 137'674'620
min: 713519.95750000002, 1425308.4903124999, 11.300000000000001
max: 713672.61843749997, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04442
INFO(indexer.cpp:1656): start indexing chunk r06024
filesize: 136'109'241
min: 713672.61843749997, 1425155.8293749997, 11.300000000000001
max: 713825.27937500004, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04440
INFO(indexer.cpp:1656): start indexing chunk r06026
filesize: 142'393'545
min: 713672.61843749997, 1425308.4903124999, 11.300000000000001
max: 713825.27937500004, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04424
INFO(indexer.cpp:1656): start indexing chunk r06040
filesize: 243'097'929
min: 713825.27937500004, 1424850.5074999998, 11.300000000000001
max: 713977.94031250011, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04260
INFO(indexer.cpp:1656): start indexing chunk r06042
filesize: 227'099'268
min: 713825.27937500004, 1425003.1684374998, 11.300000000000001
max: 713977.94031250011, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04240
INFO(indexer.cpp:1656): start indexing chunk r06044
filesize: 137'474'172
min: 713977.94031250011, 1424850.5074999998, 11.300000000000001
max: 714130.60125000007, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04602
INFO(indexer.cpp:1656): start indexing chunk r06046
filesize: 156'650'247
min: 713977.94031250011, 1425003.1684374998, 11.300000000000001
max: 714130.60125000007, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04622
INFO(indexer.cpp:1656): start indexing chunk r06060
filesize: 211'830'633
min: 713825.27937500004, 1425155.8293749997, 11.300000000000001
max: 713977.94031250011, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04600
INFO(indexer.cpp:1656): start indexing chunk r06062
filesize: 204'583'509
min: 713825.27937500004, 1425308.4903124999, 11.300000000000001
max: 713977.94031250011, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04604
INFO(indexer.cpp:1656): start indexing chunk r06064
filesize: 120'207'780
min: 713977.94031250011, 1425155.8293749997, 11.300000000000001
max: 714130.60125000007, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04620
INFO(indexer.cpp:1699): finished indexing chunk r04626
INFO(indexer.cpp:1656): start indexing chunk r06066
filesize: 100'037'997
min: 713977.94031250011, 1425308.4903124999, 11.300000000000001
max: 714130.60125000007, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r06200
filesize: 129'744'639
min: 713519.95750000002, 1425461.1512499999, 11.300000000000001
max: 713672.61843749997, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04624
INFO(indexer.cpp:1656): start indexing chunk r06202
filesize: 146'314'026
min: 713519.95750000002, 1425613.8121874998, 11.300000000000001
max: 713672.61843749997, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04426
INFO(indexer.cpp:1656): start indexing chunk r06204
filesize: 123'984'648
min: 713672.61843749997, 1425461.1512499999, 11.300000000000001
max: 713825.27937500004, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04606
INFO(indexer.cpp:1656): start indexing chunk r06206
filesize: 111'735'342
min: 713672.61843749997, 1425613.8121874998, 11.300000000000001
max: 713825.27937500004, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r04242
INFO(indexer.cpp:1699): finished indexing chunk r0446
INFO(indexer.cpp:1656): start indexing chunk r06220
filesize: 127'916'901
min: 713519.95750000002, 1425766.473125, 11.300000000000001
max: 713672.61843749997, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r06222
filesize: 114'737'553
min: 713519.95750000002, 1425919.1340625, 11.300000000000001
max: 713672.61843749997, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06004
INFO(indexer.cpp:1656): start indexing chunk r06224
filesize: 132'761'673
min: 713672.61843749997, 1425766.473125, 11.300000000000001
max: 713825.27937500004, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06000
INFO(indexer.cpp:1656): start indexing chunk r06226
filesize: 143'993'997
min: 713672.61843749997, 1425919.1340625, 11.300000000000001
max: 713825.27937500004, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06002
INFO(indexer.cpp:1656): start indexing chunk r06240
filesize: 183'414'861
min: 713825.27937500004, 1425461.1512499999, 11.300000000000001
max: 713977.94031250011, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06024
INFO(indexer.cpp:1656): start indexing chunk r06242
filesize: 170'834'562
min: 713825.27937500004, 1425613.8121874998, 11.300000000000001
max: 713977.94031250011, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06020
INFO(indexer.cpp:1656): start indexing chunk r06244
filesize: 109'453'815
min: 713977.94031250011, 1425461.1512499999, 11.300000000000001
max: 714130.60125000007, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06022
INFO(indexer.cpp:1656): start indexing chunk r06246
filesize: 103'534'605
min: 713977.94031250011, 1425613.8121874998, 11.300000000000001
max: 714130.60125000007, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06006
INFO(indexer.cpp:1656): start indexing chunk r06260
filesize: 178'132'770
min: 713825.27937500004, 1425766.473125, 11.300000000000001
max: 713977.94031250011, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06026
INFO(indexer.cpp:1656): start indexing chunk r06262
filesize: 205'496'487
min: 713825.27937500004, 1425919.1340625, 11.300000000000001
max: 713977.94031250011, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06044
INFO(indexer.cpp:1656): start indexing chunk r06264
filesize: 105'637'743
min: 713977.94031250011, 1425766.473125, 11.300000000000001
max: 714130.60125000007, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06066
INFO(indexer.cpp:1656): start indexing chunk r06266
filesize: 112'717'359
min: 713977.94031250011, 1425919.1340625, 11.300000000000001
max: 714130.60125000007, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06064
INFO(indexer.cpp:1656): start indexing chunk r06400
filesize: 161'174'691
min: 714130.60125000007, 1424850.5074999998, 11.300000000000001
max: 714283.26218750002, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06046
INFO(indexer.cpp:1656): start indexing chunk r06402
filesize: 175'950'333
min: 714130.60125000007, 1425003.1684374998, 11.300000000000001
max: 714283.26218750002, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06204
INFO(indexer.cpp:1656): start indexing chunk r06404
filesize: 159'498'099
min: 714283.26218750002, 1424850.5074999998, 11.300000000000001
max: 714435.92312500009, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06200
INFO(indexer.cpp:1656): start indexing chunk r06406
filesize: 156'647'925
min: 714283.26218750002, 1425003.1684374998, 11.300000000000001
max: 714435.92312500009, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06206
INFO(indexer.cpp:1656): start indexing chunk r06420
filesize: 147'432'123
min: 714130.60125000007, 1425155.8293749997, 11.300000000000001
max: 714283.26218750002, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06202
INFO(indexer.cpp:1656): start indexing chunk r06422
filesize: 152'890'983
min: 714130.60125000007, 1425308.4903124999, 11.300000000000001
max: 714283.26218750002, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06222
INFO(indexer.cpp:1656): start indexing chunk r06424
filesize: 183'901'509
min: 714283.26218750002, 1425155.8293749997, 11.300000000000001
max: 714435.92312500009, 1425308.4903124999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06220
INFO(indexer.cpp:1656): start indexing chunk r06426
filesize: 157'242'870
min: 714283.26218750002, 1425308.4903124999, 11.300000000000001
max: 714435.92312500009, 1425461.1512499999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06224
INFO(indexer.cpp:1656): start indexing chunk r06440
filesize: 158'761'134
min: 714435.92312500009, 1424850.5074999998, 11.300000000000001
max: 714588.58406250016, 1425003.1684374998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06246
INFO(indexer.cpp:1656): start indexing chunk r06442
filesize: 120'994'884
min: 714435.92312500009, 1425003.1684374998, 11.300000000000001
max: 714588.58406250016, 1425155.8293749997, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06226
INFO(indexer.cpp:1656): start indexing chunk r0646
filesize: 154'357'083
min: 714435.92312500009, 1425155.8293749997, 11.300000000000001
max: 714741.24500000011, 1425461.1512499999, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06244
INFO(indexer.cpp:1656): start indexing chunk r06600
filesize: 151'491'654
min: 714130.60125000007, 1425461.1512499999, 11.300000000000001
max: 714283.26218750002, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06264
INFO(indexer.cpp:1656): start indexing chunk r06602
filesize: 146'064'087
min: 714130.60125000007, 1425613.8121874998, 11.300000000000001
max: 714283.26218750002, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0464
INFO(indexer.cpp:1656): start indexing chunk r06604
filesize: 126'451'368
min: 714283.26218750002, 1425461.1512499999, 11.300000000000001
max: 714435.92312500009, 1425613.8121874998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06060
INFO(indexer.cpp:1656): start indexing chunk r06606
filesize: 118'326'717
min: 714283.26218750002, 1425613.8121874998, 11.300000000000001
max: 714435.92312500009, 1425766.473125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06266
INFO(indexer.cpp:1656): start indexing chunk r06620
filesize: 169'893'504
min: 714130.60125000007, 1425766.473125, 11.300000000000001
max: 714283.26218750002, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06040
INFO(indexer.cpp:1656): start indexing chunk r06622
filesize: 137'203'902
min: 714130.60125000007, 1425919.1340625, 11.300000000000001
max: 714283.26218750002, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0466
INFO(indexer.cpp:1656): start indexing chunk r06624
filesize: 130'896'405
min: 714283.26218750002, 1425766.473125, 11.300000000000001
max: 714435.92312500009, 1425919.1340625, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06062
INFO(indexer.cpp:1656): start indexing chunk r06626
filesize: 113'841'909
min: 714283.26218750002, 1425919.1340625, 11.300000000000001
max: 714435.92312500009, 1426071.7949999999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06042
INFO(indexer.cpp:1656): start indexing chunk r0664
filesize: 167'644'998
min: 714435.92312500009, 1425461.1512499999, 11.300000000000001
max: 714741.24500000011, 1425766.473125, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06240
INFO(indexer.cpp:1656): start indexing chunk r0666
filesize: 211'077'387
min: 714435.92312500009, 1425766.473125, 11.300000000000001
max: 714741.24500000011, 1426071.7949999999, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06242
INFO(indexer.cpp:1656): start indexing chunk r2000
filesize: 57'665'493
min: 712298.67000000004, 1426071.7949999999, 11.300000000000001
max: 712603.99187500007, 1426377.1168749998, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06422
INFO(indexer.cpp:1656): start indexing chunk r2002
filesize: 104'583'366
min: 712298.67000000004, 1426377.1168749998, 11.300000000000001
max: 712603.99187500007, 1426682.43875, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06420
INFO(indexer.cpp:1656): start indexing chunk r2004
filesize: 120'930'975
min: 712603.99187500007, 1426071.7949999999, 11.300000000000001
max: 712909.31374999997, 1426377.1168749998, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06400
INFO(indexer.cpp:1656): start indexing chunk r2006
filesize: 140'429'052
min: 712603.99187500007, 1426377.1168749998, 11.300000000000001
max: 712909.31374999997, 1426682.43875, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06406
INFO(indexer.cpp:1656): start indexing chunk r2020
filesize: 99'720'234
min: 712298.67000000004, 1426682.43875, 11.300000000000001
max: 712603.99187500007, 1426987.7606250001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06260
INFO(indexer.cpp:1656): start indexing chunk r2022
filesize: 105'770'070
min: 712298.67000000004, 1426987.7606250001, 11.300000000000001
max: 712603.99187500007, 1427293.0825, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06404
INFO(indexer.cpp:1656): start indexing chunk r2024
filesize: 140'208'435
min: 712603.99187500007, 1426682.43875, 11.300000000000001
max: 712909.31374999997, 1426987.7606250001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06402
INFO(indexer.cpp:1656): start indexing chunk r2026
filesize: 107'629'182
min: 712603.99187500007, 1426987.7606250001, 11.300000000000001
max: 712909.31374999997, 1427293.0825, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r06426
INFO(indexer.cpp:1656): start indexing chunk r20400
filesize: 149'075'343
min: 712909.31374999997, 1426071.7949999999, 11.300000000000001
max: 713061.97468749993, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06442
INFO(indexer.cpp:1656): start indexing chunk r20402
filesize: 150'348'420
min: 712909.31374999997, 1426224.4559374999, 11.300000000000001
max: 713061.97468749993, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06606
INFO(indexer.cpp:1656): start indexing chunk r20404
filesize: 114'718'140
min: 713061.97468749993, 1426071.7949999999, 11.300000000000001
max: 713214.635625, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2000
INFO(indexer.cpp:1656): start indexing chunk r20406
filesize: 148'840'011
min: 713061.97468749993, 1426224.4559374999, 11.300000000000001
max: 713214.635625, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06440
INFO(indexer.cpp:1656): start indexing chunk r20420
filesize: 167'438'232
min: 712909.31374999997, 1426377.1168749998, 11.300000000000001
max: 713061.97468749993, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06262
INFO(indexer.cpp:1656): start indexing chunk r20422
filesize: 157'375'413
min: 712909.31374999997, 1426529.7778125, 11.300000000000001
max: 713061.97468749993, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06602
INFO(indexer.cpp:1656): start indexing chunk r20424
filesize: 126'966'339
min: 713061.97468749993, 1426377.1168749998, 11.300000000000001
max: 713214.635625, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06604
INFO(indexer.cpp:1656): start indexing chunk r20426
filesize: 112'502'547
min: 713061.97468749993, 1426529.7778125, 11.300000000000001
max: 713214.635625, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06424
INFO(indexer.cpp:1656): start indexing chunk r20440
filesize: 111'057'669
min: 713214.635625, 1426071.7949999999, 11.300000000000001
max: 713367.29656250007, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06622
INFO(indexer.cpp:1656): start indexing chunk r20442
filesize: 104'954'211
min: 713214.635625, 1426224.4559374999, 11.300000000000001
max: 713367.29656250007, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06626
INFO(indexer.cpp:1656): start indexing chunk r20444
filesize: 148'856'049
min: 713367.29656250007, 1426071.7949999999, 11.300000000000001
max: 713519.95750000002, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06600
INFO(indexer.cpp:1656): start indexing chunk r20446
filesize: 176'096'997
min: 713367.29656250007, 1426224.4559374999, 11.300000000000001
max: 713519.95750000002, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06624
INFO(indexer.cpp:1656): start indexing chunk r20460
filesize: 101'910'798
min: 713214.635625, 1426377.1168749998, 11.300000000000001
max: 713367.29656250007, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r06620
INFO(indexer.cpp:1656): start indexing chunk r20462
filesize: 98'691'264
min: 713214.635625, 1426529.7778125, 11.300000000000001
max: 713367.29656250007, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2002
INFO(indexer.cpp:1656): start indexing chunk r20464
filesize: 159'241'464
min: 713367.29656250007, 1426377.1168749998, 11.300000000000001
max: 713519.95750000002, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0646
INFO(indexer.cpp:1656): start indexing chunk r20466
filesize: 138'551'877
min: 713367.29656250007, 1426529.7778125, 11.300000000000001
max: 713519.95750000002, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2020
INFO(indexer.cpp:1656): start indexing chunk r20600
filesize: 134'029'053
min: 712909.31374999997, 1426682.43875, 11.300000000000001
max: 713061.97468749993, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2022
INFO(indexer.cpp:1656): start indexing chunk r20602
filesize: 125'897'409
min: 712909.31374999997, 1426835.0996874999, 11.300000000000001
max: 713061.97468749993, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2026
INFO(indexer.cpp:1656): start indexing chunk r20604
filesize: 128'873'808
min: 713061.97468749993, 1426682.43875, 11.300000000000001
max: 713214.635625, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2004
INFO(indexer.cpp:1656): start indexing chunk r20606
filesize: 129'380'274
min: 713061.97468749993, 1426835.0996874999, 11.300000000000001
max: 713214.635625, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2006
INFO(indexer.cpp:1656): start indexing chunk r20620
filesize: 125'322'984
min: 712909.31374999997, 1426987.7606250001, 11.300000000000001
max: 713061.97468749993, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20404
INFO(indexer.cpp:1656): start indexing chunk r20622
filesize: 110'910'627
min: 712909.31374999997, 1427140.4215625001, 11.300000000000001
max: 713061.97468749993, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2024
INFO(indexer.cpp:1656): start indexing chunk r20624
filesize: 144'658'116
min: 713061.97468749993, 1426987.7606250001, 11.300000000000001
max: 713214.635625, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20400
INFO(indexer.cpp:1656): start indexing chunk r20626
filesize: 138'774'222
min: 713061.97468749993, 1427140.4215625001, 11.300000000000001
max: 713214.635625, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20442
INFO(indexer.cpp:1656): start indexing chunk r20640
filesize: 110'271'105
min: 713214.635625, 1426682.43875, 11.300000000000001
max: 713367.29656250007, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20440
INFO(indexer.cpp:1656): start indexing chunk r20642
filesize: 130'934'880
min: 713214.635625, 1426835.0996874999, 11.300000000000001
max: 713367.29656250007, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20426
INFO(indexer.cpp:1656): start indexing chunk r20644
filesize: 142'287'327
min: 713367.29656250007, 1426682.43875, 11.300000000000001
max: 713519.95750000002, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20402
INFO(indexer.cpp:1656): start indexing chunk r20646
filesize: 147'514'932
min: 713367.29656250007, 1426835.0996874999, 11.300000000000001
max: 713519.95750000002, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20424
INFO(indexer.cpp:1656): start indexing chunk r20660
filesize: 139'965'354
min: 713214.635625, 1426987.7606250001, 11.300000000000001
max: 713367.29656250007, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20460
INFO(indexer.cpp:1656): start indexing chunk r20662
filesize: 157'043'043
min: 713214.635625, 1427140.4215625001, 11.300000000000001
max: 713367.29656250007, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20406
INFO(indexer.cpp:1656): start indexing chunk r20664
filesize: 158'958'666
min: 713367.29656250007, 1426987.7606250001, 11.300000000000001
max: 713519.95750000002, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20462
INFO(indexer.cpp:1656): start indexing chunk r20666
filesize: 184'822'722
min: 713367.29656250007, 1427140.4215625001, 11.300000000000001
max: 713519.95750000002, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20444
INFO(indexer.cpp:1656): start indexing chunk r2200
filesize: 98'670'798
min: 712298.67000000004, 1427293.0825, 11.300000000000001
max: 712603.99187500007, 1427598.4043749999, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r20420
INFO(indexer.cpp:1656): start indexing chunk r2202
filesize: 107'315'874
min: 712298.67000000004, 1427598.4043749999, 11.300000000000001
max: 712603.99187500007, 1427903.7262500001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r0664
INFO(indexer.cpp:1656): start indexing chunk r2204
filesize: 136'756'080
min: 712603.99187500007, 1427293.0825, 11.300000000000001
max: 712909.31374999997, 1427598.4043749999, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r20422
INFO(indexer.cpp:1699): finished indexing chunk r20466
INFO(indexer.cpp:1656): start indexing chunk r2206
filesize: 138'960'576
min: 712603.99187500007, 1427598.4043749999, 11.300000000000001
max: 712909.31374999997, 1427903.7262500001, 316.62187500000874
INFO(indexer.cpp:1656): start indexing chunk r222
filesize: 110'364'768
min: 712298.67000000004, 1427903.7262500001, 11.300000000000001
max: 712909.31374999997, 1428514.3700000001, 621.94375000001742
INFO(indexer.cpp:1699): finished indexing chunk r20600
INFO(indexer.cpp:1656): start indexing chunk r22400
filesize: 134'111'673
min: 712909.31374999997, 1427293.0825, 11.300000000000001
max: 713061.97468749993, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20464
INFO(indexer.cpp:1656): start indexing chunk r22402
filesize: 97'495'893
min: 712909.31374999997, 1427445.7434375, 11.300000000000001
max: 713061.97468749993, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20602
INFO(indexer.cpp:1656): start indexing chunk r22404
filesize: 143'935'191
min: 713061.97468749993, 1427293.0825, 11.300000000000001
max: 713214.635625, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20604
INFO(indexer.cpp:1656): start indexing chunk r22406
filesize: 143'654'310
min: 713061.97468749993, 1427445.7434375, 11.300000000000001
max: 713214.635625, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r0666
INFO(indexer.cpp:1656): start indexing chunk r22420
filesize: 116'357'661
min: 712909.31374999997, 1427598.4043749999, 11.300000000000001
max: 713061.97468749993, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20640
INFO(indexer.cpp:1656): start indexing chunk r22422
filesize: 126'216'144
min: 712909.31374999997, 1427751.0653125001, 11.300000000000001
max: 713061.97468749993, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20622
INFO(indexer.cpp:1656): start indexing chunk r22424
filesize: 134'637'957
min: 713061.97468749993, 1427598.4043749999, 11.300000000000001
max: 713214.635625, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20606
INFO(indexer.cpp:1656): start indexing chunk r22426
filesize: 132'576'669
min: 713061.97468749993, 1427751.0653125001, 11.300000000000001
max: 713214.635625, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20626
INFO(indexer.cpp:1656): start indexing chunk r22440
filesize: 155'292'876
min: 713214.635625, 1427293.0825, 11.300000000000001
max: 713367.29656250007, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20446
INFO(indexer.cpp:1656): start indexing chunk r22442
filesize: 157'903'128
min: 713214.635625, 1427445.7434375, 11.300000000000001
max: 713367.29656250007, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20620
INFO(indexer.cpp:1656): start indexing chunk r22444
filesize: 138'199'095
min: 713367.29656250007, 1427293.0825, 11.300000000000001
max: 713519.95750000002, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20642
INFO(indexer.cpp:1656): start indexing chunk r22446
filesize: 164'012'499
min: 713367.29656250007, 1427445.7434375, 11.300000000000001
max: 713519.95750000002, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2202
INFO(indexer.cpp:1656): start indexing chunk r22460
filesize: 174'785'958
min: 713214.635625, 1427598.4043749999, 11.300000000000001
max: 713367.29656250007, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20644
INFO(indexer.cpp:1699): finished indexing chunk r20624
INFO(indexer.cpp:1656): start indexing chunk r22462
filesize: 137'038'230
min: 713214.635625, 1427751.0653125001, 11.300000000000001
max: 713367.29656250007, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r22464
filesize: 165'854'763
min: 713367.29656250007, 1427598.4043749999, 11.300000000000001
max: 713519.95750000002, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2200
INFO(indexer.cpp:1656): start indexing chunk r22466
filesize: 164'528'199
min: 713367.29656250007, 1427751.0653125001, 11.300000000000001
max: 713519.95750000002, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r222
INFO(indexer.cpp:1699): finished indexing chunk r20660
INFO(indexer.cpp:1656): start indexing chunk r22600
filesize: 122'954'733
min: 712909.31374999997, 1427903.7262500001, 11.300000000000001
max: 713061.97468749993, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r22602
filesize: 68'758'281
min: 712909.31374999997, 1428056.3871875, 11.300000000000001
max: 713061.97468749993, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22402
INFO(indexer.cpp:1656): start indexing chunk r22604
filesize: 109'109'349
min: 713061.97468749993, 1427903.7262500001, 11.300000000000001
max: 713214.635625, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20646
INFO(indexer.cpp:1656): start indexing chunk r22606
filesize: 120'254'436
min: 713061.97468749993, 1428056.3871875, 11.300000000000001
max: 713214.635625, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20664
INFO(indexer.cpp:1656): start indexing chunk r22640
filesize: 151'167'627
min: 713214.635625, 1427903.7262500001, 11.300000000000001
max: 713367.29656250007, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r20662
INFO(indexer.cpp:1656): start indexing chunk r22642
filesize: 126'195'057
min: 713214.635625, 1428056.3871875, 11.300000000000001
max: 713367.29656250007, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22420
INFO(indexer.cpp:1656): start indexing chunk r22644
filesize: 163'550'340
min: 713367.29656250007, 1427903.7262500001, 11.300000000000001
max: 713519.95750000002, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2206
INFO(indexer.cpp:1699): finished indexing chunk r22400
INFO(indexer.cpp:1656): start indexing chunk r22646
filesize: 149'910'831
min: 713367.29656250007, 1428056.3871875, 11.300000000000001
max: 713519.95750000002, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r2266
filesize: 4'336'038
min: 713214.635625, 1428209.0481250002, 11.300000000000001
max: 713519.95750000002, 1428514.3700000001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r20666
INFO(indexer.cpp:1656): start indexing chunk r24000
filesize: 112'157'838
min: 713519.95750000002, 1426071.7949999999, 11.300000000000001
max: 713672.61843749997, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2204
INFO(indexer.cpp:1656): start indexing chunk r24002
filesize: 146'894'337
min: 713519.95750000002, 1426224.4559374999, 11.300000000000001
max: 713672.61843749997, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22602
INFO(indexer.cpp:1656): start indexing chunk r24004
filesize: 157'300'083
min: 713672.61843749997, 1426071.7949999999, 11.300000000000001
max: 713825.27937500004, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2266
INFO(indexer.cpp:1656): start indexing chunk r24006
filesize: 151'459'308
min: 713672.61843749997, 1426224.4559374999, 11.300000000000001
max: 713825.27937500004, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22422
INFO(indexer.cpp:1656): start indexing chunk r24020
filesize: 127'230'048
min: 713519.95750000002, 1426377.1168749998, 11.300000000000001
max: 713672.61843749997, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22406
INFO(indexer.cpp:1656): start indexing chunk r24022
filesize: 111'909'708
min: 713519.95750000002, 1426529.7778125, 11.300000000000001
max: 713672.61843749997, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22424
INFO(indexer.cpp:1656): start indexing chunk r24024
filesize: 159'078'303
min: 713672.61843749997, 1426377.1168749998, 11.300000000000001
max: 713825.27937500004, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22404
INFO(indexer.cpp:1656): start indexing chunk r24026
filesize: 155'940'147
min: 713672.61843749997, 1426529.7778125, 11.300000000000001
max: 713825.27937500004, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22426
INFO(indexer.cpp:1656): start indexing chunk r24040
filesize: 196'396'407
min: 713825.27937500004, 1426071.7949999999, 11.300000000000001
max: 713977.94031250011, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22444
INFO(indexer.cpp:1656): start indexing chunk r24042
filesize: 183'939'390
min: 713825.27937500004, 1426224.4559374999, 11.300000000000001
max: 713977.94031250011, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22604
INFO(indexer.cpp:1656): start indexing chunk r24044
filesize: 86'332'122
min: 713977.94031250011, 1426071.7949999999, 11.300000000000001
max: 714130.60125000007, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22600
INFO(indexer.cpp:1656): start indexing chunk r24046
filesize: 96'947'145
min: 713977.94031250011, 1426224.4559374999, 11.300000000000001
max: 714130.60125000007, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22462
INFO(indexer.cpp:1656): start indexing chunk r24060
filesize: 178'238'259
min: 713825.27937500004, 1426377.1168749998, 11.300000000000001
max: 713977.94031250011, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22446
INFO(indexer.cpp:1656): start indexing chunk r24062
filesize: 165'190'671
min: 713825.27937500004, 1426529.7778125, 11.300000000000001
max: 713977.94031250011, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22606
INFO(indexer.cpp:1656): start indexing chunk r24064
filesize: 98'294'256
min: 713977.94031250011, 1426377.1168749998, 11.300000000000001
max: 714130.60125000007, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22440
INFO(indexer.cpp:1656): start indexing chunk r24066
filesize: 106'164'000
min: 713977.94031250011, 1426529.7778125, 11.300000000000001
max: 714130.60125000007, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22442
INFO(indexer.cpp:1656): start indexing chunk r24200
filesize: 163'975'941
min: 713519.95750000002, 1426682.43875, 11.300000000000001
max: 713672.61843749997, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22460
INFO(indexer.cpp:1656): start indexing chunk r24202
filesize: 180'877'266
min: 713519.95750000002, 1426835.0996874999, 11.300000000000001
max: 713672.61843749997, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22464
INFO(indexer.cpp:1656): start indexing chunk r24204
filesize: 145'980'279
min: 713672.61843749997, 1426682.43875, 11.300000000000001
max: 713825.27937500004, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22640
INFO(indexer.cpp:1656): start indexing chunk r24206
filesize: 145'118'574
min: 713672.61843749997, 1426835.0996874999, 11.300000000000001
max: 713825.27937500004, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22466
INFO(indexer.cpp:1656): start indexing chunk r24220
filesize: 147'128'076
min: 713519.95750000002, 1426987.7606250001, 11.300000000000001
max: 713672.61843749997, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24000
INFO(indexer.cpp:1656): start indexing chunk r24222
filesize: 145'043'784
min: 713519.95750000002, 1427140.4215625001, 11.300000000000001
max: 713672.61843749997, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22642
INFO(indexer.cpp:1656): start indexing chunk r24224
filesize: 142'436'016
min: 713672.61843749997, 1426987.7606250001, 11.300000000000001
max: 713825.27937500004, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24044
INFO(indexer.cpp:1656): start indexing chunk r24226
filesize: 134'890'380
min: 713672.61843749997, 1427140.4215625001, 11.300000000000001
max: 713825.27937500004, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24022
INFO(indexer.cpp:1699): finished indexing chunk r22646
INFO(indexer.cpp:1656): start indexing chunk r24240
filesize: 158'197'428
min: 713825.27937500004, 1426682.43875, 11.300000000000001
max: 713977.94031250011, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r22644
INFO(indexer.cpp:1656): start indexing chunk r24242
filesize: 193'153'977
min: 713825.27937500004, 1426835.0996874999, 11.300000000000001
max: 713977.94031250011, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r24244
filesize: 129'085'434
min: 713977.94031250011, 1426682.43875, 11.300000000000001
max: 714130.60125000007, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24046
INFO(indexer.cpp:1656): start indexing chunk r24246
filesize: 155'633'427
min: 713977.94031250011, 1426835.0996874999, 11.300000000000001
max: 714130.60125000007, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24020
INFO(indexer.cpp:1656): start indexing chunk r24260
filesize: 165'027'429
min: 713825.27937500004, 1426987.7606250001, 11.300000000000001
max: 713977.94031250011, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24004
INFO(indexer.cpp:1656): start indexing chunk r24262
filesize: 143'793'927
min: 713825.27937500004, 1427140.4215625001, 11.300000000000001
max: 713977.94031250011, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24064
INFO(indexer.cpp:1656): start indexing chunk r24264
filesize: 167'858'757
min: 713977.94031250011, 1426987.7606250001, 11.300000000000001
max: 714130.60125000007, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24002
INFO(indexer.cpp:1656): start indexing chunk r24266
filesize: 143'699'454
min: 713977.94031250011, 1427140.4215625001, 11.300000000000001
max: 714130.60125000007, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24066
INFO(indexer.cpp:1656): start indexing chunk r24400
filesize: 144'587'052
min: 714130.60125000007, 1426071.7949999999, 11.300000000000001
max: 714283.26218750002, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24006
INFO(indexer.cpp:1656): start indexing chunk r24402
filesize: 135'072'873
min: 714130.60125000007, 1426224.4559374999, 11.300000000000001
max: 714283.26218750002, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24024
INFO(indexer.cpp:1656): start indexing chunk r24404
filesize: 110'287'683
min: 714283.26218750002, 1426071.7949999999, 11.300000000000001
max: 714435.92312500009, 1426224.4559374999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24026
INFO(indexer.cpp:1656): start indexing chunk r24406
filesize: 159'356'646
min: 714283.26218750002, 1426224.4559374999, 11.300000000000001
max: 714435.92312500009, 1426377.1168749998, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24206
INFO(indexer.cpp:1656): start indexing chunk r24420
filesize: 154'132'848
min: 714130.60125000007, 1426377.1168749998, 11.300000000000001
max: 714283.26218750002, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24200
INFO(indexer.cpp:1656): start indexing chunk r24422
filesize: 150'063'354
min: 714130.60125000007, 1426529.7778125, 11.300000000000001
max: 714283.26218750002, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24244
INFO(indexer.cpp:1656): start indexing chunk r24424
filesize: 83'312'496
min: 714283.26218750002, 1426377.1168749998, 11.300000000000001
max: 714435.92312500009, 1426529.7778125, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24202
INFO(indexer.cpp:1656): start indexing chunk r24426
filesize: 105'916'086
min: 714283.26218750002, 1426529.7778125, 11.300000000000001
max: 714435.92312500009, 1426682.43875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24220
INFO(indexer.cpp:1656): start indexing chunk r2444
filesize: 236'202'750
min: 714435.92312500009, 1426071.7949999999, 11.300000000000001
max: 714741.24500000011, 1426377.1168749998, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r24226
INFO(indexer.cpp:1656): start indexing chunk r2446
filesize: 263'925'027
min: 714435.92312500009, 1426377.1168749998, 11.300000000000001
max: 714741.24500000011, 1426682.43875, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r24204
INFO(indexer.cpp:1656): start indexing chunk r24600
filesize: 148'422'240
min: 714130.60125000007, 1426682.43875, 11.300000000000001
max: 714283.26218750002, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24042
INFO(indexer.cpp:1656): start indexing chunk r24602
filesize: 142'701'696
min: 714130.60125000007, 1426835.0996874999, 11.300000000000001
max: 714283.26218750002, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24222
INFO(indexer.cpp:1699): finished indexing chunk r24224
INFO(indexer.cpp:1656): start indexing chunk r24604
filesize: 93'176'379
min: 714283.26218750002, 1426682.43875, 11.300000000000001
max: 714435.92312500009, 1426835.0996874999, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r24606
filesize: 133'191'432
min: 714283.26218750002, 1426835.0996874999, 11.300000000000001
max: 714435.92312500009, 1426987.7606250001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24246
INFO(indexer.cpp:1656): start indexing chunk r24620
filesize: 170'016'084
min: 714130.60125000007, 1426987.7606250001, 11.300000000000001
max: 714283.26218750002, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24060
INFO(indexer.cpp:1699): finished indexing chunk r24040
INFO(indexer.cpp:1656): start indexing chunk r24622
filesize: 131'403'384
min: 714130.60125000007, 1427140.4215625001, 11.300000000000001
max: 714283.26218750002, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r24624
filesize: 185'066'937
min: 714283.26218750002, 1426987.7606250001, 11.300000000000001
max: 714435.92312500009, 1427140.4215625001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24062
INFO(indexer.cpp:1656): start indexing chunk r24626
filesize: 127'087'488
min: 714283.26218750002, 1427140.4215625001, 11.300000000000001
max: 714435.92312500009, 1427293.0825, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24262
INFO(indexer.cpp:1656): start indexing chunk r2464
filesize: 203'941'449
min: 714435.92312500009, 1426682.43875, 11.300000000000001
max: 714741.24500000011, 1426987.7606250001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r24240
INFO(indexer.cpp:1656): start indexing chunk r2466
filesize: 160'081'866
min: 714435.92312500009, 1426987.7606250001, 11.300000000000001
max: 714741.24500000011, 1427293.0825, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r24400
INFO(indexer.cpp:1656): start indexing chunk r26000
filesize: 138'617'703
min: 713519.95750000002, 1427293.0825, 11.300000000000001
max: 713672.61843749997, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24242
INFO(indexer.cpp:1656): start indexing chunk r26002
filesize: 152'644'878
min: 713519.95750000002, 1427445.7434375, 11.300000000000001
max: 713672.61843749997, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24260
INFO(indexer.cpp:1656): start indexing chunk r26004
filesize: 146'094'705
min: 713672.61843749997, 1427293.0825, 11.300000000000001
max: 713825.27937500004, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24402
INFO(indexer.cpp:1656): start indexing chunk r26006
filesize: 145'414'764
min: 713672.61843749997, 1427445.7434375, 11.300000000000001
max: 713825.27937500004, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24266
INFO(indexer.cpp:1656): start indexing chunk r26020
filesize: 165'794'310
min: 713519.95750000002, 1427598.4043749999, 11.300000000000001
max: 713672.61843749997, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24424
INFO(indexer.cpp:1656): start indexing chunk r26022
filesize: 165'035'475
min: 713519.95750000002, 1427751.0653125001, 11.300000000000001
max: 713672.61843749997, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24404
INFO(indexer.cpp:1656): start indexing chunk r26024
filesize: 173'917'071
min: 713672.61843749997, 1427598.4043749999, 11.300000000000001
max: 713825.27937500004, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24264
INFO(indexer.cpp:1656): start indexing chunk r26026
filesize: 161'783'217
min: 713672.61843749997, 1427751.0653125001, 11.300000000000001
max: 713825.27937500004, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24426
INFO(indexer.cpp:1656): start indexing chunk r26040
filesize: 153'399'069
min: 713825.27937500004, 1427293.0825, 11.300000000000001
max: 713977.94031250011, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24604
INFO(indexer.cpp:1656): start indexing chunk r26042
filesize: 149'411'736
min: 713825.27937500004, 1427445.7434375, 11.300000000000001
max: 713977.94031250011, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24626
INFO(indexer.cpp:1656): start indexing chunk r26044
filesize: 149'616'369
min: 713977.94031250011, 1427293.0825, 11.300000000000001
max: 714130.60125000007, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24606
INFO(indexer.cpp:1656): start indexing chunk r26046
filesize: 132'923'214
min: 713977.94031250011, 1427445.7434375, 11.300000000000001
max: 714130.60125000007, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24420
INFO(indexer.cpp:1699): finished indexing chunk r24422
INFO(indexer.cpp:1656): start indexing chunk r26060
filesize: 131'212'089
min: 713825.27937500004, 1427598.4043749999, 11.300000000000001
max: 713977.94031250011, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r26062
filesize: 139'119'687
min: 713825.27937500004, 1427751.0653125001, 11.300000000000001
max: 713977.94031250011, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24622
INFO(indexer.cpp:1656): start indexing chunk r26064
filesize: 140'812'992
min: 713977.94031250011, 1427598.4043749999, 11.300000000000001
max: 714130.60125000007, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24602
INFO(indexer.cpp:1656): start indexing chunk r26066
filesize: 133'242'543
min: 713977.94031250011, 1427751.0653125001, 11.300000000000001
max: 714130.60125000007, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24600
INFO(indexer.cpp:1699): finished indexing chunk r24406
INFO(indexer.cpp:1656): start indexing chunk r26200
filesize: 163'138'023
min: 713519.95750000002, 1427903.7262500001, 11.300000000000001
max: 713672.61843749997, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r26202
filesize: 148'776'642
min: 713519.95750000002, 1428056.3871875, 11.300000000000001
max: 713672.61843749997, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24620
INFO(indexer.cpp:1656): start indexing chunk r26204
filesize: 158'364'234
min: 713672.61843749997, 1427903.7262500001, 11.300000000000001
max: 713825.27937500004, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26000
INFO(indexer.cpp:1656): start indexing chunk r26206
filesize: 169'034'877
min: 713672.61843749997, 1428056.3871875, 11.300000000000001
max: 713825.27937500004, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26004
INFO(indexer.cpp:1656): start indexing chunk r2622
filesize: 126'755'523
min: 713519.95750000002, 1428209.0481250002, 11.300000000000001
max: 713825.27937500004, 1428514.3700000001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r26002
INFO(indexer.cpp:1656): start indexing chunk r26240
filesize: 143'369'703
min: 713825.27937500004, 1427903.7262500001, 11.300000000000001
max: 713977.94031250011, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26006
INFO(indexer.cpp:1656): start indexing chunk r26242
filesize: 147'787'173
min: 713825.27937500004, 1428056.3871875, 11.300000000000001
max: 713977.94031250011, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2466
INFO(indexer.cpp:1656): start indexing chunk r26244
filesize: 141'567'642
min: 713977.94031250011, 1427903.7262500001, 11.300000000000001
max: 714130.60125000007, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r24624
INFO(indexer.cpp:1656): start indexing chunk r26246
filesize: 166'049'217
min: 713977.94031250011, 1428056.3871875, 11.300000000000001
max: 714130.60125000007, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26020
INFO(indexer.cpp:1656): start indexing chunk r26260
filesize: 138'282'120
min: 713825.27937500004, 1428209.0481250002, 11.300000000000001
max: 713977.94031250011, 1428361.7090625002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26022
INFO(indexer.cpp:1656): start indexing chunk r26262
filesize: 16'690'536
min: 713825.27937500004, 1428361.7090625002, 11.300000000000001
max: 713977.94031250011, 1428514.3700000001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2464
INFO(indexer.cpp:1656): start indexing chunk r26264
filesize: 146'407'905
min: 713977.94031250011, 1428209.0481250002, 11.300000000000001
max: 714130.60125000007, 1428361.7090625002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26024
INFO(indexer.cpp:1656): start indexing chunk r26266
filesize: 33'178'491
min: 713977.94031250011, 1428361.7090625002, 11.300000000000001
max: 714130.60125000007, 1428514.3700000001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26262
INFO(indexer.cpp:1656): start indexing chunk r26400
filesize: 127'056'762
min: 714130.60125000007, 1427293.0825, 11.300000000000001
max: 714283.26218750002, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26040
INFO(indexer.cpp:1656): start indexing chunk r26402
filesize: 150'615'045
min: 714130.60125000007, 1427445.7434375, 11.300000000000001
max: 714283.26218750002, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2444
INFO(indexer.cpp:1656): start indexing chunk r26404
filesize: 163'933'875
min: 714283.26218750002, 1427293.0825, 11.300000000000001
max: 714435.92312500009, 1427445.7434375, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26026
INFO(indexer.cpp:1656): start indexing chunk r26406
filesize: 179'203'806
min: 714283.26218750002, 1427445.7434375, 11.300000000000001
max: 714435.92312500009, 1427598.4043749999, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2446
INFO(indexer.cpp:1656): start indexing chunk r26420
filesize: 144'824'922
min: 714130.60125000007, 1427598.4043749999, 11.300000000000001
max: 714283.26218750002, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26060
INFO(indexer.cpp:1656): start indexing chunk r26422
filesize: 124'821'081
min: 714130.60125000007, 1427751.0653125001, 11.300000000000001
max: 714283.26218750002, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26266
INFO(indexer.cpp:1656): start indexing chunk r26424
filesize: 178'894'629
min: 714283.26218750002, 1427598.4043749999, 11.300000000000001
max: 714435.92312500009, 1427751.0653125001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26044
INFO(indexer.cpp:1656): start indexing chunk r26426
filesize: 142'963'218
min: 714283.26218750002, 1427751.0653125001, 11.300000000000001
max: 714435.92312500009, 1427903.7262500001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26064
INFO(indexer.cpp:1656): start indexing chunk r2644
filesize: 243'577'476
min: 714435.92312500009, 1427293.0825, 11.300000000000001
max: 714741.24500000011, 1427598.4043749999, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r26062
INFO(indexer.cpp:1656): start indexing chunk r2646
filesize: 257'087'871
min: 714435.92312500009, 1427598.4043749999, 11.300000000000001
max: 714741.24500000011, 1427903.7262500001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r26042
INFO(indexer.cpp:1699): finished indexing chunk r26046
INFO(indexer.cpp:1656): start indexing chunk r26600
filesize: 153'614'394
min: 714130.60125000007, 1427903.7262500001, 11.300000000000001
max: 714283.26218750002, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1656): start indexing chunk r26602
filesize: 143'584'083
min: 714130.60125000007, 1428056.3871875, 11.300000000000001
max: 714283.26218750002, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r2622
INFO(indexer.cpp:1656): start indexing chunk r26604
filesize: 176'816'817
min: 714283.26218750002, 1427903.7262500001, 11.300000000000001
max: 714435.92312500009, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26066
INFO(indexer.cpp:1656): start indexing chunk r26606
filesize: 197'952'606
min: 714283.26218750002, 1428056.3871875, 11.300000000000001
max: 714435.92312500009, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26202
INFO(indexer.cpp:1656): start indexing chunk r26620
filesize: 148'645'719
min: 714130.60125000007, 1428209.0481250002, 11.300000000000001
max: 714283.26218750002, 1428361.7090625002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26204
INFO(indexer.cpp:1656): start indexing chunk r26622
filesize: 111'801'303
min: 714130.60125000007, 1428361.7090625002, 11.300000000000001
max: 714283.26218750002, 1428514.3700000001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26200
INFO(indexer.cpp:1656): start indexing chunk r26624
filesize: 180'024'525
min: 714283.26218750002, 1428209.0481250002, 11.300000000000001
max: 714435.92312500009, 1428361.7090625002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26206
INFO(indexer.cpp:1656): start indexing chunk r26626
filesize: 145'346'238
min: 714283.26218750002, 1428361.7090625002, 11.300000000000001
max: 714435.92312500009, 1428514.3700000001, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26240
INFO(indexer.cpp:1656): start indexing chunk r26640
filesize: 162'470'124
min: 714435.92312500009, 1427903.7262500001, 11.300000000000001
max: 714588.58406250016, 1428056.3871875, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26244
INFO(indexer.cpp:1656): start indexing chunk r26642
filesize: 146'644'479
min: 714435.92312500009, 1428056.3871875, 11.300000000000001
max: 714588.58406250016, 1428209.0481250002, 163.96093750000438
INFO(indexer.cpp:1699): finished indexing chunk r26242
INFO(indexer.cpp:1656): start indexing chunk r2666
filesize: 137'236'545
min: 714435.92312500009, 1428209.0481250002, 11.300000000000001
max: 714741.24500000011, 1428514.3700000001, 316.62187500000874
INFO(indexer.cpp:1699): finished indexing chunk r26400
INFO(indexer.cpp:1699): finished indexing chunk r26246
INFO(indexer.cpp:1699): finished indexing chunk r26264
INFO(indexer.cpp:1699): finished indexing chunk r26260
INFO(indexer.cpp:1699): finished indexing chunk r26422
INFO(indexer.cpp:1699): finished indexing chunk r26402
INFO(indexer.cpp:1699): finished indexing chunk r26420
INFO(indexer.cpp:1699): finished indexing chunk r26426
INFO(indexer.cpp:1699): finished indexing chunk r26622
INFO(indexer.cpp:1699): finished indexing chunk r26404
INFO(indexer.cpp:1699): finished indexing chunk r26602
INFO(indexer.cpp:1699): finished indexing chunk r26406
INFO(indexer.cpp:1699): finished indexing chunk r26620
INFO(indexer.cpp:1699): finished indexing chunk r26600
INFO(indexer.cpp:1699): finished indexing chunk r26424
INFO(indexer.cpp:1699): finished indexing chunk r26604
INFO(indexer.cpp:1699): finished indexing chunk r26626
INFO(indexer.cpp:1699): finished indexing chunk r2666
INFO(indexer.cpp:1699): finished indexing chunk r26642
INFO(indexer.cpp:1699): finished indexing chunk r26606
INFO(indexer.cpp:1699): finished indexing chunk r26640
INFO(indexer.cpp:1699): finished indexing chunk r26624
INFO(indexer.cpp:1699): finished indexing chunk r2644
INFO(indexer.cpp:1699): finished indexing chunk r2646
